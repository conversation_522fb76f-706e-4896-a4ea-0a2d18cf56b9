/**
 * Test Database Setup Utilities
 * Provides utilities for setting up and managing test databases
 */

import * as bcrypt from 'bcrypt';
import { sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import * as schema from '../src/database/schema/index.js';

export type TestDatabase = ReturnType<typeof drizzle<typeof schema>>;

let testDbInstance: TestDatabase | null = null;
let testClient: postgres.Sql | null = null;

/**
 * Create a test database connection
 */
export async function createTestDatabase(): Promise<TestDatabase> {
  if (testDbInstance) {
    return testDbInstance;
  }

  // Use a separate test database
  const testDbName = `rsglider_test_${Date.now()}_${Math.random().toString(36).substring(7)}`;

  // First connect to postgres to create the test database
  const adminClient = postgres({
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    user: process.env.DATABASE_USER || 'rsglider',
    password: process.env.DATABASE_PASSWORD || 'rsglider_dev_password',
    database: 'postgres', // Connect to default postgres database
  });

  try {
    // Create test database
    await adminClient.unsafe(`CREATE DATABASE "${testDbName}"`);
  } catch (error) {
    console.warn('Test database might already exist:', error.message);
  } finally {
    await adminClient.end();
  }

  // Connect to the test database
  testClient = postgres({
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    user: process.env.DATABASE_USER || 'rsglider',
    password: process.env.DATABASE_PASSWORD || 'rsglider_dev_password',
    database: testDbName,
  });

  testDbInstance = drizzle(testClient, { schema });

  // Run migrations
  try {
    await migrate(testDbInstance, { migrationsFolder: './src/database/migrations' });
  } catch (error) {
    console.warn('Migration error (might be expected):', error.message);
  }

  // Manually add bio column if it doesn't exist (for testing)
  try {
    await testClient`ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT`;
  } catch (error) {
    console.warn('Bio column might already exist:', error.message);
  }

  return testDbInstance;
}

/**
 * Clean all data from test database tables
 */
export async function cleanTestDatabase(db: TestDatabase): Promise<void> {
  // Get all table names from schema
  const tableNames = [
    'user_sessions',
    'user_roles',
    'role_permissions',
    'refresh_tokens',
    'file_uploads',
    'gitea_repositories',
    'gitea_profiles',
    'devices',
    'client_releases',
    'marketplace_items',
    'users',
    'roles',
    'permissions',
  ];

  // Disable foreign key checks temporarily
  await db.execute(sql`SET session_replication_role = replica`);

  try {
    // Truncate all tables
    for (const tableName of tableNames) {
      try {
        await db.execute(sql.raw(`TRUNCATE TABLE "${tableName}" RESTART IDENTITY CASCADE`));
      } catch (error) {
        // Table might not exist, continue
        console.warn(`Could not truncate table ${tableName}:`, error.message);
      }
    }
  } finally {
    // Re-enable foreign key checks
    await db.execute(sql`SET session_replication_role = DEFAULT`);
  }
}

/**
 * Seed test database with basic data
 */
export async function seedTestDatabase(db: TestDatabase): Promise<{
  testUser: any;
  adminUser: any;
  testRole: any;
  adminRole: any;
}> {
  const { users, roles, userRoles } = schema;

  // Create roles
  const [testRole] = await db.insert(roles).values({
    name: 'user',
    description: 'Standard user role',
    isSystemRole: true,
  }).returning();

  const [adminRole] = await db.insert(roles).values({
    name: 'admin',
    description: 'Administrator role',
    isSystemRole: true,
  }).returning();

  // Hash the test password
  const hashedPassword = await bcrypt.hash('password123', 12);

  // Create test users
  const [testUser] = await db.insert(users).values({
    email: '<EMAIL>',
    password: hashedPassword,
    name: 'Test User',
    roles: ['user'],
    isActive: true,
    emailVerified: true,
    emailVerificationToken: null,
  }).returning();

  const [adminUser] = await db.insert(users).values({
    email: '<EMAIL>',
    password: hashedPassword,
    name: 'Admin User',
    roles: ['admin', 'user'],
    isActive: true,
    emailVerified: true,
    emailVerificationToken: null,
  }).returning();

  // Assign roles to users
  await db.insert(userRoles).values([
    { userId: testUser.id, roleId: testRole.id },
    { userId: adminUser.id, roleId: testRole.id },
    { userId: adminUser.id, roleId: adminRole.id },
  ]);

  return {
    testUser,
    adminUser,
    testRole,
    adminRole,
  };
}

/**
 * Close test database connection and cleanup
 */
export async function closeTestDatabase(): Promise<void> {
  if (testClient) {
    await testClient.end();
    testClient = null;
    testDbInstance = null;
  }
}

/**
 * Create a mock file for testing
 */
export function createMockFile(overrides: Partial<Express.Multer.File> = {}): Express.Multer.File {
  return {
    fieldname: 'file',
    originalname: 'test.png',
    encoding: '7bit',
    mimetype: 'image/png',
    size: 1024,
    buffer: Buffer.from('fake-image-data'),
    destination: '',
    filename: '',
    path: '',
    stream: null as any,
    ...overrides,
  };
}

/**
 * Create mock S3 service for testing
 */
export function createMockS3Service() {
  return {
    defaultBucket: 'test-bucket', // Add the defaultBucket property
    uploadFile: jest.fn().mockResolvedValue({
      key: 'mock-key',
      etag: '"mock-etag-123"',
      location: 'https://mock-bucket.s3.amazonaws.com/mock-key',
    }),
    getPresignedUploadUrl: jest.fn().mockResolvedValue(
      'https://mock-bucket.s3.amazonaws.com/presigned-url'
    ),
    getPresignedDownloadUrl: jest.fn().mockResolvedValue(
      'https://mock-bucket.s3.amazonaws.com/download-url'
    ),
    deleteFile: jest.fn().mockResolvedValue(undefined),
    fileExists: jest.fn().mockResolvedValue(true),
    getFileMetadata: jest.fn().mockResolvedValue({
      contentType: 'image/png',
      contentLength: 1024,
      etag: '"mock-etag-123"',
      lastModified: new Date(),
    }),
  };
}

/**
 * Create mock JWT payload
 */
export function createMockJwtPayload(overrides: any = {}) {
  return {
    sub: 'test-user-id',
    email: '<EMAIL>',
    roles: ['user'],
    sid: 'test-session-id',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
    ...overrides,
  };
}
