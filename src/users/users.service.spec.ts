import { BadRequestException, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';

// Mock external dependencies
jest.mock('bcrypt', () => ({
  compare: jest.fn(),
  hash: jest.fn(),
}));
jest.mock('speakeasy', () => ({
  generateSecret: jest.fn(),
  totp: {
    verify: jest.fn(),
  },
}));
jest.mock('qrcode', () => ({
  toDataURL: jest.fn(),
}));

const mockedBcrypt = {
  compare: jest.fn(),
  hash: jest.fn(),
} as any;
const mockedSpeakeasy = {
  generateSecret: jest.fn(),
  totp: {
    verify: jest.fn(),
  },
} as any;
const mockedQrcode = {
  toDataURL: jest.fn(),
} as any;

describe('UsersService', () => {
  let service: UsersService;
  let mockDb: any;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    roles: ['user'],
    twoFactorEnabled: false,
    twoFactorSecret: null,
    emailVerified: true,
    isActive: true,
    bio: 'Test bio',
    lastLoginAt: new Date('2023-01-01'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockDevice = {
    id: 'device-123',
    userId: 'user-123',
    deviceName: 'Test Device',
    deviceType: 'mobile',
    platform: 'iOS',
    deviceInfo: { model: 'iPhone 14', version: '16.0' },
    isTrusted: false,
    registeredAt: new Date('2023-01-01'),
    lastActivityAt: new Date('2023-01-01'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockSession = {
    id: 'session-123',
    userId: 'user-123',
    sessionType: 'web',
    ipAddress: '127.0.0.1',
    userAgent: 'Mozilla/5.0',
    isActive: true,
    isCurrent: false,
    expiresAt: new Date('2024-01-01'),
    lastActivityAt: new Date('2023-01-01'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    revokedAt: null,
  };

  beforeEach(async () => {
    // Mock database operations
    mockDb = {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      returning: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      filter: jest.fn().mockReturnThis(),
      map: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('findById', () => {
    it('should call getCurrentUser with the provided userId', async () => {
      const getCurrentUserSpy = jest.spyOn(service, 'getCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      await service.findById('user-123');

      expect(getCurrentUserSpy).toHaveBeenCalledWith('user-123');
    });
  });

  describe('findByEmail', () => {
    it('should return user when found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });
    });

    it('should return null when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toBeNull();
    });

    it('should handle user with single name', async () => {
      const singleNameUser = { ...mockUser, name: 'SingleName' };
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([singleNameUser]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result?.firstName).toBe('SingleName');
      expect(result?.lastName).toBe('');
    });
  });

  describe('getCurrentUser', () => {
    it('should return user when found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.getCurrentUser('user-123');

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.getCurrentUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getCurrentUser('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle inactive user', async () => {
      const inactiveUser = { ...mockUser, isActive: false };
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([inactiveUser]);

      const result = await service.getCurrentUser('user-123');

      expect(result.status).toBe('inactive');
    });
  });

  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const updateData = { name: 'Updated Name', bio: 'Updated bio' };
      const updatedUser = { ...mockUser, ...updateData };

      // Mock email check (no existing user)
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([updatedUser]);

      const result = await service.updateProfile('user-123', updateData);

      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('Name');
      expect(result.bio).toBe('Updated bio');
    });

    it('should throw ConflictException for duplicate email', async () => {
      const updateData = { email: '<EMAIL>' };

      // Mock email check (existing user found)
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([{ id: 'other-user' }]);

      await expect(service.updateProfile('user-123', updateData)).rejects.toThrow(ConflictException);
      await expect(service.updateProfile('user-123', updateData)).rejects.toThrow('Email already exists');
    });

    it('should throw NotFoundException when user not found during update', async () => {
      const updateData = { name: 'Updated Name' };

      // Mock email check (no existing user)
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      // Mock update operation (no user found)
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.updateProfile('nonexistent', updateData)).rejects.toThrow(NotFoundException);
      await expect(service.updateProfile('nonexistent', updateData)).rejects.toThrow('User not found');
    });
  });

  describe('updateCurrentUser', () => {
    it('should update current user successfully', async () => {
      const updateData = { firstName: 'Updated', lastName: 'Name', avatar: 'avatar-url' };
      const updatedUser = { ...mockUser, name: 'Updated Name' };

      // Mock get current user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([mockUser]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([updatedUser]);

      const result = await service.updateCurrentUser('user-123', updateData);

      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('Name');
      expect(result.avatar).toBe('avatar-url');
    });

    it('should throw NotFoundException when user not found', async () => {
      const updateData = { firstName: 'Updated' };

      // Mock get current user (not found)
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.updateCurrentUser('nonexistent', updateData)).rejects.toThrow(NotFoundException);
      await expect(service.updateCurrentUser('nonexistent', updateData)).rejects.toThrow('User not found');
    });

    it('should throw NotFoundException when update fails', async () => {
      const updateData = { firstName: 'Updated' };

      // Mock get current user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([mockUser]);

      // Mock update operation (fails)
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.updateCurrentUser('user-123', updateData)).rejects.toThrow(NotFoundException);
    });

    it('should preserve existing name parts when not provided', async () => {
      const updateData = { firstName: 'Updated' }; // Only firstName provided
      const updatedUser = { ...mockUser, name: 'Updated User' };

      // Mock get current user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([mockUser]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([updatedUser]);

      const result = await service.updateCurrentUser('user-123', updateData);

      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('User');
    });
  });

  describe('deactivateUser', () => {
    it('should deactivate user successfully', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([{ id: 'user-123' }]);

      const result = await service.deactivateUser('user-123');

      expect(result).toEqual({ message: 'User deactivated successfully' });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.deactivateUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.deactivateUser('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle database errors', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockRejectedValue(new Error('Database error'));

      await expect(service.deactivateUser('user-123')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUserRoles', () => {
    it('should return user roles', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([{ roles: ['user', 'admin'] }]);

      const result = await service.getUserRoles('user-123');

      expect(result).toEqual(['user', 'admin']);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.getUserRoles('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getUserRoles('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle database errors', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockRejectedValue(new Error('Database error'));

      await expect(service.getUserRoles('user-123')).rejects.toThrow(NotFoundException);
    });
  });

  describe('setup2FA', () => {
    beforeEach(() => {
      mockedSpeakeasy.generateSecret.mockReturnValue({
        base32: 'TESTSECRET123',
        otpauth_url: 'otpauth://totp/RSGlider%20(<EMAIL>)?secret=TESTSECRET123&issuer=RSGlider',
      } as any);
      mockedQrcode.toDataURL.mockResolvedValue('data:image/png;base64,mockqrcode');
    });

    it('should setup 2FA successfully', async () => {
      const userWithout2FA = { ...mockUser, twoFactorEnabled: false };

      // Mock get user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([userWithout2FA]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.setup2FA('user-123');

      expect(result).toEqual({
        secret: 'TESTSECRET123',
        qrCodeUrl: 'data:image/png;base64,mockqrcode',
        manualEntryKey: 'TESTSECRET123',
        issuer: 'RSGlider',
      });
      expect(mockedSpeakeasy.generateSecret).toHaveBeenCalledWith({
        name: 'RSGlider (<EMAIL>)',
        issuer: 'RSGlider',
        length: 32,
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.setup2FA('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.setup2FA('nonexistent')).rejects.toThrow('User not found');
    });

    it('should throw BadRequestException when 2FA already enabled', async () => {
      const userWith2FA = { ...mockUser, twoFactorEnabled: true };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      await expect(service.setup2FA('user-123')).rejects.toThrow(BadRequestException);
      await expect(service.setup2FA('user-123')).rejects.toThrow('Two-factor authentication is already enabled');
    });
  });

  describe('verifySetup2FA', () => {
    it('should verify and enable 2FA successfully', async () => {
      const userWithSecret = { ...mockUser, twoFactorSecret: 'TESTSECRET123', twoFactorEnabled: false };

      // Mock get user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([userWithSecret]);

      // Mock TOTP verification
      mockedSpeakeasy.totp.verify.mockReturnValue(true);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.verifySetup2FA('user-123', '123456');

      expect(result.enabled).toBe(true);
      expect(result.backupCodes).toHaveLength(8);
      expect(result.message).toBe('Two-factor authentication has been successfully enabled');
      expect(mockedSpeakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'TESTSECRET123',
        encoding: 'base32',
        token: '123456',
        window: 2,
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.verifySetup2FA('nonexistent', '123456')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when 2FA already enabled', async () => {
      const userWith2FA = { ...mockUser, twoFactorEnabled: true };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow('Two-factor authentication is already enabled');
    });

    it('should throw BadRequestException when setup not initiated', async () => {
      const userWithoutSecret = { ...mockUser, twoFactorSecret: null, twoFactorEnabled: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWithoutSecret]);

      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow('2FA setup not initiated. Please call setup endpoint first.');
    });

    it('should throw BadRequestException for invalid verification code', async () => {
      const userWithSecret = { ...mockUser, twoFactorSecret: 'TESTSECRET123', twoFactorEnabled: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWithSecret]);

      mockedSpeakeasy.totp.verify.mockReturnValue(false);

      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow('Invalid verification code');
    });
  });

  describe('disable2FA', () => {
    it('should disable 2FA successfully with valid password and code', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        twoFactorSecret: 'TESTSECRET123',
        password: 'hashedPassword'
      };

      // Mock get user
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      // Mock password verification
      mockedBcrypt.compare.mockResolvedValue(true);

      // Mock TOTP verification
      mockedSpeakeasy.totp.verify.mockReturnValue(true);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.disable2FA('user-123', 'password123', '123456');

      expect(result.message).toBe('Two-factor authentication has been successfully disabled');
      expect(mockedBcrypt.compare).toHaveBeenCalledWith('password123', 'hashedPassword');
      expect(mockedSpeakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'TESTSECRET123',
        encoding: 'base32',
        token: '123456',
        window: 2,
      });
    });

    it('should disable 2FA successfully with valid password only', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        twoFactorSecret: 'TESTSECRET123',
        password: 'hashedPassword'
      };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      mockedBcrypt.compare.mockResolvedValue(true);

      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.disable2FA('user-123', 'password123');

      expect(result.message).toBe('Two-factor authentication has been successfully disabled');
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.disable2FA('nonexistent', 'password123')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when 2FA not enabled', async () => {
      const userWithout2FA = { ...mockUser, twoFactorEnabled: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWithout2FA]);

      await expect(service.disable2FA('user-123', 'password123')).rejects.toThrow(BadRequestException);
      await expect(service.disable2FA('user-123', 'password123')).rejects.toThrow('Two-factor authentication is not enabled');
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        password: 'hashedPassword'
      };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      mockedBcrypt.compare.mockResolvedValue(false);

      await expect(service.disable2FA('user-123', 'wrongpassword')).rejects.toThrow(UnauthorizedException);
      await expect(service.disable2FA('user-123', 'wrongpassword')).rejects.toThrow('Invalid password');
    });

    it('should throw BadRequestException for invalid verification code', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        twoFactorSecret: 'TESTSECRET123',
        password: 'hashedPassword'
      };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      mockedBcrypt.compare.mockResolvedValue(true);
      mockedSpeakeasy.totp.verify.mockReturnValue(false);

      await expect(service.disable2FA('user-123', 'password123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.disable2FA('user-123', 'password123', '123456')).rejects.toThrow('Invalid verification code');
    });
  });

  describe('getUserDevices', () => {
    it('should return user devices', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([mockDevice]);

      const result = await service.getUserDevices('user-123');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        ...mockDevice,
        registeredAt: mockDevice.registeredAt.toISOString(),
        lastActivityAt: mockDevice.lastActivityAt.toISOString(),
        createdAt: mockDevice.createdAt.toISOString(),
        updatedAt: mockDevice.updatedAt.toISOString(),
      });
    });

    it('should return empty array when no devices found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([]);

      const result = await service.getUserDevices('user-123');

      expect(result).toEqual([]);
    });
  });

  describe('registerDevice', () => {
    it('should register device successfully', async () => {
      const deviceDto = {
        deviceName: 'Test Device',
        deviceType: 'mobile' as const,
        platform: 'iOS',
        deviceInfo: { model: 'iPhone 14', version: '16.0' },
      };

      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([mockDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result).toEqual({
        ...mockDevice,
        registeredAt: mockDevice.registeredAt.toISOString(),
        lastActivityAt: mockDevice.lastActivityAt.toISOString(),
        createdAt: mockDevice.createdAt.toISOString(),
        updatedAt: mockDevice.updatedAt.toISOString(),
      });
    });
  });

  describe('removeDevice', () => {
    it('should remove device successfully', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue({ count: 1 });

      const result = await service.removeDevice('user-123', 'device-123');

      expect(result).toEqual({ message: 'Device removed' });
    });

    it('should throw NotFoundException when device not found', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue({ count: 0 });

      await expect(service.removeDevice('user-123', 'nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.removeDevice('user-123', 'nonexistent')).rejects.toThrow('Device not found');
    });
  });

  describe('getUserSessions', () => {
    it('should return active user sessions', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([mockSession]);

      const result = await service.getUserSessions('user-123');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        ...mockSession,
        expiresAt: mockSession.expiresAt.toISOString(),
        lastActivityAt: mockSession.lastActivityAt.toISOString(),
        createdAt: mockSession.createdAt.toISOString(),
        updatedAt: mockSession.updatedAt.toISOString(),
        revokedAt: null,
      });
    });

    it('should filter out expired sessions', async () => {
      const expiredSession = { ...mockSession, expiresAt: new Date('2020-01-01') };
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([expiredSession]);

      const result = await service.getUserSessions('user-123');

      expect(result).toEqual([]);
    });
  });

  describe('removeUserSession', () => {
    it('should remove user session successfully', async () => {
      const sessionToRemove = { ...mockSession, isCurrent: false, isActive: true };

      // Mock get session
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([sessionToRemove]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.removeUserSession('user-123', 'session-123');

      expect(result).toEqual({ message: 'Session revoked' });
    });

    it('should throw NotFoundException when session not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([]);

      await expect(service.removeUserSession('user-123', 'nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.removeUserSession('user-123', 'nonexistent')).rejects.toThrow('Session not found');
    });

    it('should throw BadRequestException when trying to delete current session', async () => {
      const currentSession = { ...mockSession, isCurrent: true };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([currentSession]);

      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow(BadRequestException);
      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow('Cannot delete the current session');
    });

    it('should throw BadRequestException when session already revoked', async () => {
      const revokedSession = { ...mockSession, isCurrent: false, isActive: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([revokedSession]);

      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow(BadRequestException);
      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow('Session already revoked');
    });
  });

  describe('removeAllUserSessions', () => {
    it('should remove all user sessions except current', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.removeAllUserSessions('user-123');

      expect(result).toEqual({ message: 'All other sessions revoked' });
    });

    it('should include IP address when request provided', async () => {
      const mockRequest = { ip: '***********' } as any;

      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.removeAllUserSessions('user-123', mockRequest);

      expect(result).toEqual({ message: 'All other sessions revoked' });
    });
  });

  describe('adminListUsers', () => {
    it('should list users with pagination', async () => {
      const mockUsers = [mockUser];

      // Mock count query
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 1 }]);

      // Mock users query
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce(mockUsers);

      const result = await service.adminListUsers({ page: 1, limit: 20 });

      expect(result.users).toHaveLength(1);
      expect(result.pagination).toEqual({ page: 1, limit: 20, total: 1 });
    });

    it('should filter users by search term', async () => {
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 0 }]);

      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      const result = await service.adminListUsers({ search: '<EMAIL>' });

      expect(result.users).toEqual([]);
    });

    it('should filter users by status', async () => {
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 0 }]);

      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      const result = await service.adminListUsers({ status: 'active' });

      expect(result.users).toEqual([]);
    });

    it('should filter users by role', async () => {
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 0 }]);

      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      const result = await service.adminListUsers({ role: 'admin' });

      expect(result.users).toEqual([]);
    });
  });

  describe('adminGetUser', () => {
    it('should call getCurrentUser', async () => {
      const getCurrentUserSpy = jest.spyOn(service, 'getCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      await service.adminGetUser('user-123');

      expect(getCurrentUserSpy).toHaveBeenCalledWith('user-123');
    });
  });

  describe('adminCreateUser', () => {
    it('should create user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        roles: ['user'],
      };

      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([{ ...mockUser, email: '<EMAIL>', name: 'New User' }]);

      const result = await service.adminCreateUser(userData);

      expect(result.email).toBe('<EMAIL>');
      expect(result.firstName).toBe('New');
      expect(result.lastName).toBe('User');
    });
  });

  describe('adminUpdateUser', () => {
    it('should call updateCurrentUser', async () => {
      const updateCurrentUserSpy = jest.spyOn(service, 'updateCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Updated',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      const updateData = { firstName: 'Updated' };
      await service.adminUpdateUser('user-123', updateData);

      expect(updateCurrentUserSpy).toHaveBeenCalledWith('user-123', updateData);
    });
  });

  describe('adminDeleteUser', () => {
    it('should delete user successfully', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([mockUser]);

      const result = await service.adminDeleteUser('user-123');

      expect(result).toEqual({ message: 'User deleted' });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.adminDeleteUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.adminDeleteUser('nonexistent')).rejects.toThrow('User not found');
    });
  });
});
