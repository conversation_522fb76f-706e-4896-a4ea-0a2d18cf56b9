import { BadRequestException, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';

// Mock external dependencies
jest.mock('bcrypt', () => ({
  compare: jest.fn(),
  hash: jest.fn(),
}));
jest.mock('speakeasy', () => ({
  generateSecret: jest.fn(),
  totp: {
    verify: jest.fn(),
  },
}));
jest.mock('qrcode', () => ({
  toDataURL: jest.fn(),
}));

const mockedBcrypt = {
  compare: jest.fn(),
  hash: jest.fn(),
} as any;
const mockedSpeakeasy = {
  generateSecret: jest.fn(),
  totp: {
    verify: jest.fn(),
  },
} as any;
const mockedQrcode = {
  toDataURL: jest.fn(),
} as any;

describe('UsersService', () => {
  let service: UsersService;
  let mockDb: any;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    roles: ['user'],
    twoFactorEnabled: false,
    twoFactorSecret: null,
    emailVerified: true,
    isActive: true,
    bio: 'Test bio',
    lastLoginAt: new Date('2023-01-01'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockDevice = {
    id: 'device-123',
    userId: 'user-123',
    deviceName: 'Test Device',
    deviceType: 'mobile',
    platform: 'iOS',
    deviceInfo: { model: 'iPhone 14', version: '16.0' },
    isTrusted: false,
    registeredAt: new Date('2023-01-01'),
    lastActivityAt: new Date('2023-01-01'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockSession = {
    id: 'session-123',
    userId: 'user-123',
    sessionType: 'web',
    ipAddress: '127.0.0.1',
    userAgent: 'Mozilla/5.0',
    isActive: true,
    isCurrent: false,
    expiresAt: new Date('2024-01-01'),
    lastActivityAt: new Date('2023-01-01'),
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    revokedAt: null,
  };

  beforeEach(async () => {
    // Mock database operations
    mockDb = {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      returning: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      filter: jest.fn().mockReturnThis(),
      map: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('findById', () => {
    it('should call getCurrentUser with the provided userId', async () => {
      const getCurrentUserSpy = jest.spyOn(service, 'getCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      await service.findById('user-123');

      expect(getCurrentUserSpy).toHaveBeenCalledWith('user-123');
    });
  });

  describe('findByEmail', () => {
    it('should return user when found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });
    });

    it('should return null when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toBeNull();
    });

    it('should handle user with single name', async () => {
      const singleNameUser = { ...mockUser, name: 'SingleName' };
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([singleNameUser]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result?.firstName).toBe('SingleName');
      expect(result?.lastName).toBe('');
    });
  });

  describe('getCurrentUser', () => {
    it('should return user when found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.getCurrentUser('user-123');

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.getCurrentUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getCurrentUser('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle inactive user', async () => {
      const inactiveUser = { ...mockUser, isActive: false };
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([inactiveUser]);

      const result = await service.getCurrentUser('user-123');

      expect(result.status).toBe('inactive');
    });
  });

  describe('updateProfile', () => {
    it('should update user profile successfully', async () => {
      const updateData = { name: 'Updated Name', bio: 'Updated bio' };
      const updatedUser = { ...mockUser, ...updateData };

      // Mock email check (no existing user)
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([updatedUser]);

      const result = await service.updateProfile('user-123', updateData);

      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('Name');
      expect(result.bio).toBe('Updated bio');
    });

    it('should throw ConflictException for duplicate email', async () => {
      const updateData = { email: '<EMAIL>' };

      // Mock email check (existing user found)
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([{ id: 'other-user' }]);

      await expect(service.updateProfile('user-123', updateData)).rejects.toThrow(ConflictException);
      await expect(service.updateProfile('user-123', updateData)).rejects.toThrow('Email already exists');
    });

    it('should throw NotFoundException when user not found during update', async () => {
      const updateData = { name: 'Updated Name' };

      // Mock email check (no existing user)
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      // Mock update operation (no user found)
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.updateProfile('nonexistent', updateData)).rejects.toThrow(NotFoundException);
      await expect(service.updateProfile('nonexistent', updateData)).rejects.toThrow('User not found');
    });
  });

  describe('updateCurrentUser', () => {
    it('should update current user successfully', async () => {
      const updateData = { firstName: 'Updated', lastName: 'Name', avatar: 'avatar-url' };
      const updatedUser = { ...mockUser, name: 'Updated Name' };

      // Mock get current user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([mockUser]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([updatedUser]);

      const result = await service.updateCurrentUser('user-123', updateData);

      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('Name');
      expect(result.avatar).toBe('avatar-url');
    });

    it('should throw NotFoundException when user not found', async () => {
      const updateData = { firstName: 'Updated' };

      // Mock get current user (not found)
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.updateCurrentUser('nonexistent', updateData)).rejects.toThrow(NotFoundException);
      await expect(service.updateCurrentUser('nonexistent', updateData)).rejects.toThrow('User not found');
    });

    it('should throw NotFoundException when update fails', async () => {
      const updateData = { firstName: 'Updated' };

      // Mock get current user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([mockUser]);

      // Mock update operation (fails)
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.updateCurrentUser('user-123', updateData)).rejects.toThrow(NotFoundException);
    });

    it('should preserve existing name parts when not provided', async () => {
      const updateData = { firstName: 'Updated' }; // Only firstName provided
      const updatedUser = { ...mockUser, name: 'Updated User' };

      // Mock get current user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([mockUser]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([updatedUser]);

      const result = await service.updateCurrentUser('user-123', updateData);

      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('User');
    });
  });

  describe('deactivateUser', () => {
    it('should deactivate user successfully', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([{ id: 'user-123' }]);

      const result = await service.deactivateUser('user-123');

      expect(result).toEqual({ message: 'User deactivated successfully' });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.deactivateUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.deactivateUser('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle database errors', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockRejectedValue(new Error('Database error'));

      await expect(service.deactivateUser('user-123')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUserRoles', () => {
    it('should return user roles', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([{ roles: ['user', 'admin'] }]);

      const result = await service.getUserRoles('user-123');

      expect(result).toEqual(['user', 'admin']);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.getUserRoles('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getUserRoles('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle database errors', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockRejectedValue(new Error('Database error'));

      await expect(service.getUserRoles('user-123')).rejects.toThrow(NotFoundException);
    });
  });

  describe('setup2FA', () => {
    beforeEach(() => {
      mockedSpeakeasy.generateSecret.mockReturnValue({
        base32: 'TESTSECRET123',
        otpauth_url: 'otpauth://totp/RSGlider%20(<EMAIL>)?secret=TESTSECRET123&issuer=RSGlider',
      } as any);
      mockedQrcode.toDataURL.mockResolvedValue('data:image/png;base64,mockqrcode' as any);
    });

    it('should setup 2FA successfully', async () => {
      const userWithout2FA = { ...mockUser, twoFactorEnabled: false };

      // Mock get user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([userWithout2FA]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.setup2FA('user-123');

      expect(result).toEqual({
        secret: 'TESTSECRET123',
        qrCodeUrl: 'data:image/png;base64,mockqrcode',
        manualEntryKey: 'TESTSECRET123',
        issuer: 'RSGlider',
      });
      expect(mockedSpeakeasy.generateSecret).toHaveBeenCalledWith({
        name: 'RSGlider (<EMAIL>)',
        issuer: 'RSGlider',
        length: 32,
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.setup2FA('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.setup2FA('nonexistent')).rejects.toThrow('User not found');
    });

    it('should throw BadRequestException when 2FA already enabled', async () => {
      const userWith2FA = { ...mockUser, twoFactorEnabled: true };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      await expect(service.setup2FA('user-123')).rejects.toThrow(BadRequestException);
      await expect(service.setup2FA('user-123')).rejects.toThrow('Two-factor authentication is already enabled');
    });
  });

  describe('verifySetup2FA', () => {
    it('should verify and enable 2FA successfully', async () => {
      const userWithSecret = { ...mockUser, twoFactorSecret: 'TESTSECRET123', twoFactorEnabled: false };

      // Mock get user
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([userWithSecret]);

      // Mock TOTP verification
      mockedSpeakeasy.totp.verify.mockReturnValue(true as any);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.verifySetup2FA('user-123', '123456');

      expect(result.enabled).toBe(true);
      expect(result.backupCodes).toHaveLength(8);
      expect(result.message).toBe('Two-factor authentication has been successfully enabled');
      expect(mockedSpeakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'TESTSECRET123',
        encoding: 'base32',
        token: '123456',
        window: 2,
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.verifySetup2FA('nonexistent', '123456')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when 2FA already enabled', async () => {
      const userWith2FA = { ...mockUser, twoFactorEnabled: true };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow('Two-factor authentication is already enabled');
    });

    it('should throw BadRequestException when setup not initiated', async () => {
      const userWithoutSecret = { ...mockUser, twoFactorSecret: null, twoFactorEnabled: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWithoutSecret]);

      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow('2FA setup not initiated. Please call setup endpoint first.');
    });

    it('should throw BadRequestException for invalid verification code', async () => {
      const userWithSecret = { ...mockUser, twoFactorSecret: 'TESTSECRET123', twoFactorEnabled: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWithSecret]);

      mockedSpeakeasy.totp.verify.mockReturnValue(false as any);

      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.verifySetup2FA('user-123', '123456')).rejects.toThrow('Invalid verification code');
    });
  });

  describe('disable2FA', () => {
    it('should disable 2FA successfully with valid password and code', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        twoFactorSecret: 'TESTSECRET123',
        password: 'hashedPassword'
      };

      // Mock get user
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      // Mock password verification
      mockedBcrypt.compare.mockResolvedValue(true as any);

      // Mock TOTP verification
      mockedSpeakeasy.totp.verify.mockReturnValue(true as any);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.disable2FA('user-123', 'password123', '123456');

      expect(result.message).toBe('Two-factor authentication has been successfully disabled');
      expect(mockedBcrypt.compare).toHaveBeenCalledWith('password123', 'hashedPassword');
      expect(mockedSpeakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'TESTSECRET123',
        encoding: 'base32',
        token: '123456',
        window: 2,
      });
    });

    it('should disable 2FA successfully with valid password only', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        twoFactorSecret: 'TESTSECRET123',
        password: 'hashedPassword'
      };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      mockedBcrypt.compare.mockResolvedValue(true as any);

      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.disable2FA('user-123', 'password123');

      expect(result.message).toBe('Two-factor authentication has been successfully disabled');
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([]);

      await expect(service.disable2FA('nonexistent', 'password123')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when 2FA not enabled', async () => {
      const userWithout2FA = { ...mockUser, twoFactorEnabled: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWithout2FA]);

      await expect(service.disable2FA('user-123', 'password123')).rejects.toThrow(BadRequestException);
      await expect(service.disable2FA('user-123', 'password123')).rejects.toThrow('Two-factor authentication is not enabled');
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        password: 'hashedPassword'
      };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      mockedBcrypt.compare.mockResolvedValue(false as any);

      await expect(service.disable2FA('user-123', 'wrongpassword')).rejects.toThrow(UnauthorizedException);
      await expect(service.disable2FA('user-123', 'wrongpassword')).rejects.toThrow('Invalid password');
    });

    it('should throw BadRequestException for invalid verification code', async () => {
      const userWith2FA = {
        ...mockUser,
        twoFactorEnabled: true,
        twoFactorSecret: 'TESTSECRET123',
        password: 'hashedPassword'
      };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.limit.mockResolvedValue([userWith2FA]);

      mockedBcrypt.compare.mockResolvedValue(true as any);
      mockedSpeakeasy.totp.verify.mockReturnValue(false as any);

      await expect(service.disable2FA('user-123', 'password123', '123456')).rejects.toThrow(BadRequestException);
      await expect(service.disable2FA('user-123', 'password123', '123456')).rejects.toThrow('Invalid verification code');
    });
  });

  describe('getUserDevices', () => {
    it('should return user devices across all platforms', async () => {
      const multiPlatformDevices = [
        {
          ...mockDevice,
          id: 'device-ios',
          deviceName: 'iPhone 14 Pro',
          deviceType: 'mobile',
          platform: 'iOS',
          deviceInfo: { model: 'iPhone 14 Pro', version: '16.0' },
        },
        {
          ...mockDevice,
          id: 'device-android',
          deviceName: 'Samsung Galaxy S23',
          deviceType: 'mobile',
          platform: 'Android',
          deviceInfo: { model: 'SM-S911B', version: '13' },
        },
        {
          ...mockDevice,
          id: 'device-windows',
          deviceName: 'Windows Desktop',
          deviceType: 'desktop',
          platform: 'Windows',
          deviceInfo: { version: '11', build: '22621' },
        },
        {
          ...mockDevice,
          id: 'device-mac',
          deviceName: 'MacBook Pro',
          deviceType: 'desktop',
          platform: 'macOS',
          deviceInfo: { model: 'MacBook Pro 16-inch', version: '13.0' },
        },
        {
          ...mockDevice,
          id: 'device-linux',
          deviceName: 'Ubuntu Workstation',
          deviceType: 'desktop',
          platform: 'Linux',
          deviceInfo: { distribution: 'Ubuntu', version: '22.04 LTS' },
        },
        {
          ...mockDevice,
          id: 'device-web',
          deviceName: 'Chrome Browser',
          deviceType: 'web',
          platform: 'Web',
          deviceInfo: { browser: 'Chrome', version: '*********' },
        },
      ];

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue(multiPlatformDevices);

      const result = await service.getUserDevices('user-123');

      expect(result).toHaveLength(6);
      expect(result.map(d => d.platform)).toEqual(['iOS', 'Android', 'Windows', 'macOS', 'Linux', 'Web']);
      expect(result.map(d => d.deviceType)).toEqual(['mobile', 'mobile', 'desktop', 'desktop', 'desktop', 'web']);
    });

    it('should return only mobile devices', async () => {
      const mobileDevices = [
        {
          ...mockDevice,
          id: 'device-ios',
          deviceName: 'iPhone',
          deviceType: 'mobile',
          platform: 'iOS',
        },
        {
          ...mockDevice,
          id: 'device-android',
          deviceName: 'Android Phone',
          deviceType: 'mobile',
          platform: 'Android',
        },
      ];

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue(mobileDevices);

      const result = await service.getUserDevices('user-123');

      expect(result).toHaveLength(2);
      expect(result.every(d => d.deviceType === 'mobile')).toBe(true);
    });

    it('should return only desktop devices', async () => {
      const desktopDevices = [
        {
          ...mockDevice,
          id: 'device-windows',
          deviceName: 'Windows PC',
          deviceType: 'desktop',
          platform: 'Windows',
        },
        {
          ...mockDevice,
          id: 'device-mac',
          deviceName: 'iMac',
          deviceType: 'desktop',
          platform: 'macOS',
        },
        {
          ...mockDevice,
          id: 'device-linux',
          deviceName: 'Linux Workstation',
          deviceType: 'desktop',
          platform: 'Linux',
        },
      ];

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue(desktopDevices);

      const result = await service.getUserDevices('user-123');

      expect(result).toHaveLength(3);
      expect(result.every(d => d.deviceType === 'desktop')).toBe(true);
      expect(result.map(d => d.platform)).toEqual(['Windows', 'macOS', 'Linux']);
    });

    it('should return web browser devices', async () => {
      const webDevices = [
        {
          ...mockDevice,
          id: 'device-chrome',
          deviceName: 'Chrome Browser',
          deviceType: 'web',
          platform: 'Web',
          deviceInfo: { browser: 'Chrome', os: 'Windows' },
        },
        {
          ...mockDevice,
          id: 'device-firefox',
          deviceName: 'Firefox Browser',
          deviceType: 'web',
          platform: 'Web',
          deviceInfo: { browser: 'Firefox', os: 'macOS' },
        },
        {
          ...mockDevice,
          id: 'device-safari',
          deviceName: 'Safari Browser',
          deviceType: 'web',
          platform: 'Web',
          deviceInfo: { browser: 'Safari', os: 'macOS' },
        },
      ];

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue(webDevices);

      const result = await service.getUserDevices('user-123');

      expect(result).toHaveLength(3);
      expect(result.every(d => d.deviceType === 'web')).toBe(true);
      expect(result.every(d => d.platform === 'Web')).toBe(true);
    });

    it('should return tablet devices', async () => {
      const tabletDevices = [
        {
          ...mockDevice,
          id: 'device-ipad',
          deviceName: 'iPad Pro',
          deviceType: 'tablet',
          platform: 'iPadOS',
          deviceInfo: { model: 'iPad Pro 12.9-inch' },
        },
        {
          ...mockDevice,
          id: 'device-android-tablet',
          deviceName: 'Samsung Galaxy Tab',
          deviceType: 'tablet',
          platform: 'Android',
          deviceInfo: { model: 'Galaxy Tab S8' },
        },
      ];

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue(tabletDevices);

      const result = await service.getUserDevices('user-123');

      expect(result).toHaveLength(2);
      expect(result.every(d => d.deviceType === 'tablet')).toBe(true);
      expect(result.map(d => d.platform)).toEqual(['iPadOS', 'Android']);
    });

    it('should return empty array when no devices found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([]);

      const result = await service.getUserDevices('user-123');

      expect(result).toEqual([]);
    });

    it('should properly format device timestamps', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([mockDevice]);

      const result = await service.getUserDevices('user-123');

      expect(result[0]).toEqual({
        ...mockDevice,
        registeredAt: mockDevice.registeredAt.toISOString(),
        lastActivityAt: mockDevice.lastActivityAt.toISOString(),
        createdAt: mockDevice.createdAt.toISOString(),
        updatedAt: mockDevice.updatedAt.toISOString(),
      });
    });
  });

  describe('registerDevice', () => {
    it('should register iOS mobile device successfully', async () => {
      const deviceDto = {
        deviceName: 'iPhone 14 Pro',
        deviceType: 'mobile' as const,
        platform: 'iOS',
        deviceInfo: { model: 'iPhone 14 Pro', version: '16.0', osVersion: 'iOS 16.0' },
      };

      const iOSDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([iOSDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('iPhone 14 Pro');
      expect(result.platform).toBe('iOS');
      expect(result.deviceType).toBe('mobile');
    });

    it('should register Android mobile device successfully', async () => {
      const deviceDto = {
        deviceName: 'Samsung Galaxy S23',
        deviceType: 'mobile' as const,
        platform: 'Android',
        deviceInfo: { model: 'SM-S911B', version: '13', manufacturer: 'Samsung' },
      };

      const androidDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([androidDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('Samsung Galaxy S23');
      expect(result.platform).toBe('Android');
      expect(result.deviceType).toBe('mobile');
    });

    it('should register Windows desktop device successfully', async () => {
      const deviceDto = {
        deviceName: 'Windows Desktop',
        deviceType: 'desktop' as const,
        platform: 'Windows',
        deviceInfo: { version: '11', build: '22621', architecture: 'x64' },
      };

      const windowsDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([windowsDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('Windows Desktop');
      expect(result.platform).toBe('Windows');
      expect(result.deviceType).toBe('desktop');
    });

    it('should register macOS desktop device successfully', async () => {
      const deviceDto = {
        deviceName: 'MacBook Pro',
        deviceType: 'desktop' as const,
        platform: 'macOS',
        deviceInfo: { model: 'MacBook Pro 16-inch', version: '13.0', chip: 'M2 Pro' },
      };

      const macDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([macDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('MacBook Pro');
      expect(result.platform).toBe('macOS');
      expect(result.deviceType).toBe('desktop');
    });

    it('should register Linux desktop device successfully', async () => {
      const deviceDto = {
        deviceName: 'Ubuntu Workstation',
        deviceType: 'desktop' as const,
        platform: 'Linux',
        deviceInfo: { distribution: 'Ubuntu', version: '22.04 LTS', kernel: '5.15.0' },
      };

      const linuxDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([linuxDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('Ubuntu Workstation');
      expect(result.platform).toBe('Linux');
      expect(result.deviceType).toBe('desktop');
    });

    it('should register web browser device successfully', async () => {
      const deviceDto = {
        deviceName: 'Chrome Browser',
        deviceType: 'web' as const,
        platform: 'Web',
        deviceInfo: {
          browser: 'Chrome',
          version: '*********',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          os: 'Windows'
        },
      };

      const webDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([webDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('Chrome Browser');
      expect(result.platform).toBe('Web');
      expect(result.deviceType).toBe('web');
    });

    it('should register tablet device successfully', async () => {
      const deviceDto = {
        deviceName: 'iPad Pro',
        deviceType: 'tablet' as const,
        platform: 'iPadOS',
        deviceInfo: { model: 'iPad Pro 12.9-inch', version: '16.0', generation: '6th' },
      };

      const tabletDevice = { ...mockDevice, ...deviceDto };
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([tabletDevice]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.deviceName).toBe('iPad Pro');
      expect(result.platform).toBe('iPadOS');
      expect(result.deviceType).toBe('tablet');
    });

    it('should set device as untrusted by default', async () => {
      const deviceDto = {
        deviceName: 'Test Device',
        deviceType: 'mobile' as const,
        platform: 'iOS',
        deviceInfo: { model: 'iPhone', version: '16.0' },
      };

      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([{ ...mockDevice, isTrusted: false }]);

      const result = await service.registerDevice('user-123', deviceDto);

      expect(result.isTrusted).toBe(false);
    });
  });

  describe('removeDevice', () => {
    it('should remove device successfully', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue({ count: 1 });

      const result = await service.removeDevice('user-123', 'device-123');

      expect(result).toEqual({ message: 'Device removed' });
    });

    it('should throw NotFoundException when device not found', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue({ count: 0 });

      await expect(service.removeDevice('user-123', 'nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.removeDevice('user-123', 'nonexistent')).rejects.toThrow('Device not found');
    });
  });

  describe('getUserSessions', () => {
    it('should return active user sessions', async () => {
      // Use a future expiry date to ensure session is not filtered out
      const activeSession = { ...mockSession, expiresAt: new Date('2025-01-01') };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([activeSession]);

      const result = await service.getUserSessions('user-123');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        ...activeSession,
        expiresAt: activeSession.expiresAt.toISOString(),
        lastActivityAt: activeSession.lastActivityAt.toISOString(),
        createdAt: activeSession.createdAt.toISOString(),
        updatedAt: activeSession.updatedAt.toISOString(),
        revokedAt: null,
      });
    });

    it('should filter out expired sessions', async () => {
      const expiredSession = { ...mockSession, expiresAt: new Date('2020-01-01') };
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([expiredSession]);

      const result = await service.getUserSessions('user-123');

      expect(result).toEqual([]);
    });
  });

  describe('removeUserSession', () => {
    it('should remove user session successfully', async () => {
      const sessionToRemove = { ...mockSession, isCurrent: false, isActive: true };

      // Mock get session
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([sessionToRemove]);

      // Mock update operation
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.removeUserSession('user-123', 'session-123');

      expect(result).toEqual({ message: 'Session revoked' });
    });

    it('should throw NotFoundException when session not found', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([]);

      await expect(service.removeUserSession('user-123', 'nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.removeUserSession('user-123', 'nonexistent')).rejects.toThrow('Session not found');
    });

    it('should throw BadRequestException when trying to delete current session', async () => {
      const currentSession = { ...mockSession, isCurrent: true };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([currentSession]);

      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow(BadRequestException);
      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow('Cannot delete the current session');
    });

    it('should throw BadRequestException when session already revoked', async () => {
      const revokedSession = { ...mockSession, isCurrent: false, isActive: false };

      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue([revokedSession]);

      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow(BadRequestException);
      await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow('Session already revoked');
    });
  });

  describe('removeAllUserSessions', () => {
    it('should remove all user sessions except current', async () => {
      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.removeAllUserSessions('user-123');

      expect(result).toEqual({ message: 'All other sessions revoked' });
    });

    it('should include IP address when request provided', async () => {
      const mockRequest = { ip: '***********' } as any;

      mockDb.update.mockReturnValue(mockDb);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);

      const result = await service.removeAllUserSessions('user-123', mockRequest);

      expect(result).toEqual({ message: 'All other sessions revoked' });
    });
  });

  describe('adminListUsers', () => {
    it('should list users with pagination', async () => {
      const mockUsers = [mockUser];

      // Mock count query
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 1 }]);

      // Mock users query
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce(mockUsers);

      const result = await service.adminListUsers({ page: 1, limit: 20 });

      expect(result.users).toHaveLength(1);
      expect(result.pagination).toEqual({ page: 1, limit: 20, total: 1 });
    });

    it('should filter users by search term', async () => {
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 0 }]);

      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      const result = await service.adminListUsers({ search: '<EMAIL>' });

      expect(result.users).toEqual([]);
    });

    it('should filter users by status', async () => {
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 0 }]);

      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      const result = await service.adminListUsers({ status: 'active' });

      expect(result.users).toEqual([]);
    });

    it('should filter users by role', async () => {
      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockResolvedValueOnce([{ count: 0 }]);

      mockDb.select.mockReturnValueOnce(mockDb);
      mockDb.from.mockReturnValueOnce(mockDb);
      mockDb.where.mockReturnValueOnce(mockDb);
      mockDb.offset.mockReturnValueOnce(mockDb);
      mockDb.limit.mockResolvedValueOnce([]);

      const result = await service.adminListUsers({ role: 'admin' });

      expect(result.users).toEqual([]);
    });
  });

  describe('adminGetUser', () => {
    it('should call getCurrentUser', async () => {
      const getCurrentUserSpy = jest.spyOn(service, 'getCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      await service.adminGetUser('user-123');

      expect(getCurrentUserSpy).toHaveBeenCalledWith('user-123');
    });
  });

  describe('adminCreateUser', () => {
    it('should create user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        firstName: 'New',
        lastName: 'User',
        roles: ['user'],
      };

      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([{ ...mockUser, email: '<EMAIL>', name: 'New User' }]);

      const result = await service.adminCreateUser(userData);

      expect(result.email).toBe('<EMAIL>');
      expect(result.firstName).toBe('New');
      expect(result.lastName).toBe('User');
    });
  });

  describe('adminUpdateUser', () => {
    it('should call updateCurrentUser', async () => {
      const updateCurrentUserSpy = jest.spyOn(service, 'updateCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Updated',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      const updateData = { firstName: 'Updated' };
      await service.adminUpdateUser('user-123', updateData);

      expect(updateCurrentUserSpy).toHaveBeenCalledWith('user-123', updateData);
    });
  });

  describe('adminDeleteUser', () => {
    it('should delete user successfully', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([mockUser]);

      const result = await service.adminDeleteUser('user-123');

      expect(result).toEqual({ message: 'User deleted' });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.delete.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([]);

      await expect(service.adminDeleteUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.adminDeleteUser('nonexistent')).rejects.toThrow('User not found');
    });
  });
});
