import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';

// Simple, focused unit tests that test business logic without complex mocking
describe('UsersService Unit Tests', () => {
  let service: UsersService;
  let mockDb: any;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'hashedPassword',
    isActive: true,
    emailVerified: true,
    twoFactorEnabled: false,
    twoFactorSecret: null,
    roles: ['user'],
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLoginAt: new Date('2023-01-01'),
  };

  beforeEach(async () => {
    // Create a mock database that properly handles Drizzle ORM chaining
    const createChainableMock = (finalResult = []) => {
      const mock = {
        select: jest.fn().mockReturnValue(mock),
        from: jest.fn().mockReturnValue(mock),
        where: jest.fn().mockReturnValue(mock),
        limit: jest.fn().mockResolvedValue(finalResult),
        offset: jest.fn().mockReturnValue(mock),
        update: jest.fn().mockReturnValue(mock),
        set: jest.fn().mockReturnValue(mock),
        returning: jest.fn().mockResolvedValue(finalResult),
        insert: jest.fn().mockReturnValue(mock),
        values: jest.fn().mockReturnValue(mock),
        delete: jest.fn().mockReturnValue(mock),
      };
      return mock;
    };

    mockDb = createChainableMock();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
        {
          provide: 'ConfigService',
          useValue: {
            get: jest.fn().mockReturnValue('test-value'),
          },
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  describe('findById', () => {
    it('should delegate to getCurrentUser', async () => {
      const getCurrentUserSpy = jest.spyOn(service, 'getCurrentUser').mockResolvedValue({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });

      await service.findById('user-123');

      expect(getCurrentUserSpy).toHaveBeenCalledWith('user-123');
    });
  });

  describe('findByEmail', () => {
    it('should return user when found', async () => {
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });
    });

    it('should return null when user not found', async () => {
      mockDb.limit.mockResolvedValue([]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result).toBeNull();
    });

    it('should handle user with single name', async () => {
      const singleNameUser = { ...mockUser, name: 'SingleName' };
      mockDb.limit.mockResolvedValue([singleNameUser]);

      const result = await service.findByEmail('<EMAIL>');

      expect(result?.firstName).toBe('SingleName');
      expect(result?.lastName).toBe('');
    });
  });

  describe('getCurrentUser', () => {
    it('should return user when found', async () => {
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.getCurrentUser('user-123');

      expect(result).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: 'Test',
        lastName: 'User',
        roles: mockUser.roles,
        twoFactorEnabled: mockUser.twoFactorEnabled,
        emailVerified: mockUser.emailVerified,
        status: 'active',
        lastLoginAt: mockUser.lastLoginAt.toISOString(),
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.limit.mockResolvedValue([]);

      await expect(service.getCurrentUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getCurrentUser('nonexistent')).rejects.toThrow('User not found');
    });

    it('should handle inactive user', async () => {
      const inactiveUser = { ...mockUser, isActive: false };
      mockDb.limit.mockResolvedValue([inactiveUser]);

      const result = await service.getCurrentUser('user-123');

      expect(result.status).toBe('inactive');
    });
  });

  // Note: updateProfile and updateCurrentUser tests are complex due to multiple DB calls
  // These are better tested in integration tests where we have real DB operations

  describe('deactivateUser', () => {
    it('should deactivate user successfully', async () => {
      mockDb.returning.mockResolvedValue([{ ...mockUser, isActive: false }]);

      const result = await service.deactivateUser('user-123');

      expect(result).toEqual({ message: 'User deactivated successfully' });
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.returning.mockResolvedValue([]);

      await expect(service.deactivateUser('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.deactivateUser('nonexistent')).rejects.toThrow('User not found');
    });
  });

  describe('getUserRoles', () => {
    it('should return user roles', async () => {
      mockDb.limit.mockResolvedValue([mockUser]);

      const result = await service.getUserRoles('user-123');

      expect(result).toEqual(['user']);
    });

    it('should throw NotFoundException when user not found', async () => {
      mockDb.limit.mockResolvedValue([]);

      await expect(service.getUserRoles('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getUserRoles('nonexistent')).rejects.toThrow('User not found');
    });
  });

  describe('Device Management', () => {
    const mockDevice = {
      id: 'device-123',
      userId: 'user-123',
      deviceName: 'Test Device',
      deviceType: 'mobile',
      platform: 'iOS',
      deviceInfo: { model: 'iPhone 14', version: '16.0' },
      isTrusted: false,
      registeredAt: new Date('2023-01-01'),
      lastActivityAt: new Date('2023-01-01'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
    };

    describe('getUserDevices', () => {
      it('should return user devices with formatted timestamps', async () => {
        // Mock the final result of the query chain
        mockDb.where = jest.fn().mockResolvedValue([mockDevice]);

        const result = await service.getUserDevices('user-123');

        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          ...mockDevice,
          registeredAt: mockDevice.registeredAt.toISOString(),
          lastActivityAt: mockDevice.lastActivityAt.toISOString(),
          createdAt: mockDevice.createdAt.toISOString(),
          updatedAt: mockDevice.updatedAt.toISOString(),
        });
      });

      it('should return empty array when no devices found', async () => {
        mockDb.where = jest.fn().mockResolvedValue([]);

        const result = await service.getUserDevices('user-123');

        expect(result).toEqual([]);
      });
    });

    describe('registerDevice', () => {
      it('should register device successfully', async () => {
        const deviceDto = {
          deviceName: 'iPhone 14 Pro',
          deviceType: 'mobile' as const,
          platform: 'iOS',
          deviceInfo: { model: 'iPhone 14 Pro', version: '16.0' },
        };

        mockDb.returning.mockResolvedValue([mockDevice]);

        const result = await service.registerDevice('user-123', deviceDto);

        expect(result).toEqual({
          ...mockDevice,
          registeredAt: mockDevice.registeredAt.toISOString(),
          lastActivityAt: mockDevice.lastActivityAt.toISOString(),
          createdAt: mockDevice.createdAt.toISOString(),
          updatedAt: mockDevice.updatedAt.toISOString(),
        });
      });
    });

    describe('removeDevice', () => {
      it('should remove device successfully', async () => {
        mockDb.where.mockResolvedValue({ count: 1 });

        const result = await service.removeDevice('user-123', 'device-123');

        expect(result).toEqual({ message: 'Device removed' });
      });

      it('should throw NotFoundException when device not found', async () => {
        mockDb.where.mockResolvedValue({ count: 0 });

        await expect(service.removeDevice('user-123', 'nonexistent')).rejects.toThrow(NotFoundException);
        await expect(service.removeDevice('user-123', 'nonexistent')).rejects.toThrow('Device not found');
      });
    });
  });

  describe('Session Management', () => {
    const mockSession = {
      id: 'session-123',
      userId: 'user-123',
      sessionType: 'web',
      userAgent: 'Mozilla/5.0',
      ipAddress: '127.0.0.1',
      isActive: true,
      isCurrent: false,
      expiresAt: new Date('2025-12-31'),
      lastActivityAt: new Date('2023-01-01'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2023-01-01'),
      revokedAt: null,
    };

    describe('getUserSessions', () => {
      it('should return active user sessions', async () => {
        mockDb.where = jest.fn().mockResolvedValue([mockSession]);

        const result = await service.getUserSessions('user-123');

        expect(result).toHaveLength(1);
        expect(result[0]).toMatchObject({
          id: mockSession.id,
          userId: mockSession.userId,
          sessionType: mockSession.sessionType,
          userAgent: mockSession.userAgent,
          ipAddress: mockSession.ipAddress,
          isActive: mockSession.isActive,
          isCurrent: mockSession.isCurrent,
          expiresAt: mockSession.expiresAt.toISOString(),
          lastActivityAt: mockSession.lastActivityAt.toISOString(),
          createdAt: mockSession.createdAt.toISOString(),
          updatedAt: mockSession.updatedAt.toISOString(),
        });
      });

      it('should filter out expired sessions', async () => {
        const expiredSession = { ...mockSession, expiresAt: new Date('2020-01-01') };
        mockDb.where = jest.fn().mockResolvedValue([expiredSession]);

        const result = await service.getUserSessions('user-123');

        expect(result).toEqual([]);
      });
    });

    describe('removeUserSession', () => {
      it('should remove user session successfully', async () => {
        const sessionToRemove = { ...mockSession, isCurrent: false, isActive: true };
        mockDb.where.mockResolvedValueOnce([sessionToRemove]);

        const result = await service.removeUserSession('user-123', 'session-123');

        expect(result).toEqual({ message: 'Session revoked' });
      });

      it('should throw NotFoundException when session not found', async () => {
        mockDb.where.mockResolvedValue([]);

        await expect(service.removeUserSession('user-123', 'nonexistent')).rejects.toThrow(NotFoundException);
        await expect(service.removeUserSession('user-123', 'nonexistent')).rejects.toThrow('Session not found');
      });

      it('should throw BadRequestException when trying to delete current session', async () => {
        const currentSession = { ...mockSession, isCurrent: true };
        mockDb.where.mockResolvedValue([currentSession]);

        await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow(BadRequestException);
        await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow('Cannot delete the current session');
      });

      it('should throw BadRequestException when session already revoked', async () => {
        const revokedSession = { ...mockSession, isCurrent: false, isActive: false };
        mockDb.where.mockResolvedValue([revokedSession]);

        await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow(BadRequestException);
        await expect(service.removeUserSession('user-123', 'session-123')).rejects.toThrow('Session already revoked');
      });
    });

    describe('removeAllUserSessions', () => {
      it('should remove all user sessions except current', async () => {
        const result = await service.removeAllUserSessions('user-123');

        expect(result).toEqual({ message: 'All other sessions revoked' });
      });
    });
  });

  describe('Admin Operations', () => {
    describe('adminListUsers', () => {
      it('should list users with pagination', async () => {
        const mockUsers = [mockUser];

        // Mock count query
        mockDb.where.mockResolvedValueOnce([{ count: 1 }]);
        // Mock users query
        mockDb.limit.mockResolvedValue(mockUsers);

        const result = await service.adminListUsers({ page: 1, limit: 20 });

        expect(result.users).toHaveLength(1);
        expect(result.pagination).toEqual({ page: 1, limit: 20, total: 1 });
      });

      it('should filter users by search term', async () => {
        mockDb.where.mockResolvedValueOnce([{ count: 0 }]);
        mockDb.limit.mockResolvedValue([]);

        const result = await service.adminListUsers({ search: '<EMAIL>' });

        expect(result.users).toEqual([]);
      });
    });

    describe('adminGetUser', () => {
      it('should delegate to getCurrentUser', async () => {
        const getCurrentUserSpy = jest.spyOn(service, 'getCurrentUser').mockResolvedValue({
          id: mockUser.id,
          email: mockUser.email,
          firstName: 'Test',
          lastName: 'User',
          roles: mockUser.roles,
          twoFactorEnabled: mockUser.twoFactorEnabled,
          emailVerified: mockUser.emailVerified,
          status: 'active',
          lastLoginAt: mockUser.lastLoginAt.toISOString(),
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        });

        await service.adminGetUser('user-123');

        expect(getCurrentUserSpy).toHaveBeenCalledWith('user-123');
      });
    });

    describe('adminCreateUser', () => {
      it('should create user successfully', async () => {
        const userData = {
          email: '<EMAIL>',
          firstName: 'New',
          lastName: 'User',
          roles: ['user'],
        };

        const newUser = { ...mockUser, email: '<EMAIL>', name: 'New User' };
        mockDb.returning.mockResolvedValue([newUser]);

        const result = await service.adminCreateUser(userData);

        expect(result.email).toBe('<EMAIL>');
        expect(result.firstName).toBe('New');
        expect(result.lastName).toBe('User');
      });
    });

    describe('adminUpdateUser', () => {
      it('should delegate to updateCurrentUser', async () => {
        const updateCurrentUserSpy = jest.spyOn(service, 'updateCurrentUser').mockResolvedValue({
          id: mockUser.id,
          email: mockUser.email,
          firstName: 'Updated',
          lastName: 'User',
          roles: mockUser.roles,
          twoFactorEnabled: mockUser.twoFactorEnabled,
          emailVerified: mockUser.emailVerified,
          status: 'active',
          lastLoginAt: mockUser.lastLoginAt.toISOString(),
          createdAt: mockUser.createdAt.toISOString(),
          updatedAt: mockUser.updatedAt.toISOString(),
        });

        const updateData = { firstName: 'Updated' };
        await service.adminUpdateUser('user-123', updateData);

        expect(updateCurrentUserSpy).toHaveBeenCalledWith('user-123', updateData);
      });
    });

    describe('adminDeleteUser', () => {
      it('should delete user successfully', async () => {
        mockDb.returning.mockResolvedValue([mockUser]);

        const result = await service.adminDeleteUser('user-123');

        expect(result).toEqual({ message: 'User deleted' });
      });

      it('should throw NotFoundException when user not found', async () => {
        mockDb.returning.mockResolvedValue([]);

        await expect(service.adminDeleteUser('nonexistent')).rejects.toThrow(NotFoundException);
        await expect(service.adminDeleteUser('nonexistent')).rejects.toThrow('User not found');
      });
    });
  });
});
